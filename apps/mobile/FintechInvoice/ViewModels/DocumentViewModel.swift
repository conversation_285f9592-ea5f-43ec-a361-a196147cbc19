import Foundation
import SwiftUI
import MessageUI

@MainActor
class DocumentViewModel: ObservableObject {
    // MARK: - Published Properties
    @Published var currentStep: DocumentCreationStep = .documentType
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?

    // Document creation data
    @Published var selectedDocumentType: DocumentType = .taxInvoice
    @Published var selectedCustomer: Customer?
    @Published var selectedProducts: [DocumentItemFormData] = []
    @Published var globalDiscountPercent: String = "0"
    @Published var notes: String = ""
    @Published var issueDate = Date()
    @Published var dueDate: Date?
    
    // Lists for selection
    @Published var customers: [Customer] = []
    @Published var products: [Product] = []
    @Published var filteredCustomers: [Customer] = []
    @Published var filteredProducts: [Product] = []
    
    // Search queries
    @Published var customerSearchQuery: String = "" {
        didSet {
            filterCustomers()
        }
    }
    @Published var productSearchQuery: String = "" {
        didSet {
            filterProducts()
        }
    }
    
    // Customer creation
    @Published var showingCreateCustomer = false
    @Published var customerFormData = CustomerFormData()
    
    // Product creation
    @Published var showingCreateProduct = false
    @Published var productFormData = ProductFormData()
    
    // Document creation result
    @Published var createdDocument: Document?
    @Published var showingDocumentSuccess = false

    // Email functionality
    @Published var showingMailComposer = false
    @Published var mailComposeResult: Result<MFMailComposeResult, MailError>?
    
    private let supabaseService = SupabaseService.shared

    // MARK: - Initializer

    init(companyId: String? = nil) {
        self.selectedCompanyId = companyId
        // All @Published properties are already initialized with default values
        // This explicit initializer resolves the ambiguous init issue
    }

    // MARK: - Computed Properties
    
    var canProceedToNextStep: Bool {
        switch currentStep {
        case .documentType:
            return true // Document type is always selected
        case .customerSelection:
            return selectedCustomer != nil
        case .productSelection:
            return !selectedProducts.isEmpty && selectedProducts.allSatisfy { $0.isValid }
        case .summary:
            return true // Summary is just for review
        }
    }
    
    var canGoBack: Bool {
        return currentStep != .documentType
    }
    
    var globalDiscountValue: Double {
        return Double(globalDiscountPercent.replacingOccurrences(of: ",", with: ".")) ?? 0.0
    }
    
    var documentSummary: DocumentItemsSummary {
        return DocumentItemsSummary(items: selectedProducts)
    }
    
    var finalSubtotal: Double {
        let subtotal = documentSummary.subtotal
        let discountAmount = subtotal * (globalDiscountValue / 100)
        return subtotal - discountAmount
    }
    
    var finalVatAmount: Double {
        return selectedProducts.reduce(0) { total, item in
            let itemSubtotal = item.lineTotal
            let itemDiscount = itemSubtotal * (globalDiscountValue / 100)
            let itemFinalSubtotal = itemSubtotal - itemDiscount
            return total + (itemFinalSubtotal * (item.vatRate / 100))
        }
    }
    
    var finalTotal: Double {
        return finalSubtotal + finalVatAmount
    }
    
    var formattedFinalSubtotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: finalSubtotal)) ?? "₪\(finalSubtotal)"
    }
    
    var formattedFinalVat: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: finalVatAmount)) ?? "₪\(finalVatAmount)"
    }
    
    var formattedFinalTotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: finalTotal)) ?? "₪\(finalTotal)"
    }
    
    // MARK: - Navigation Methods
    
    func goToNextStep() {
        guard canProceedToNextStep else { return }
        
        switch currentStep {
        case .documentType:
            currentStep = .customerSelection
            loadCustomers()
        case .customerSelection:
            currentStep = .productSelection
            loadProducts()
        case .productSelection:
            currentStep = .summary
        case .summary:
            break // Handle document creation
        }
    }
    
    func goToPreviousStep() {
        guard canGoBack else { return }
        
        switch currentStep {
        case .documentType:
            break
        case .customerSelection:
            currentStep = .documentType
        case .productSelection:
            currentStep = .customerSelection
        case .summary:
            currentStep = .productSelection
        }
    }
    
    func resetWizard() {
        currentStep = .documentType
        selectedDocumentType = .taxInvoice
        selectedCustomer = nil
        selectedProducts = []
        globalDiscountPercent = "0"
        notes = ""
        issueDate = Date()
        dueDate = nil
        customerSearchQuery = ""
        productSearchQuery = ""
        showingCreateCustomer = false
        showingCreateProduct = false
        customerFormData = CustomerFormData()
        productFormData = ProductFormData()
        createdDocument = nil
        showingDocumentSuccess = false
        errorMessage = nil
        successMessage = nil
    }
    
    // MARK: - Data Loading Methods
    
    func loadCustomers() {
        guard let companyId = getCompanyId() else { return }
        
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let loadedCustomers = try await supabaseService.getCustomers(companyId: companyId)
                await MainActor.run {
                    self.customers = loadedCustomers
                    self.filterCustomers()
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "שגיאה בטעינת לקוחות: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    func loadProducts() {
        guard let companyId = getCompanyId() else { return }
        
        isLoading = true
        errorMessage = nil
        
        Task {
            do {
                let loadedProducts = try await supabaseService.getProducts(companyId: companyId)
                await MainActor.run {
                    self.products = loadedProducts
                    self.filterProducts()
                    self.isLoading = false
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "שגיאה בטעינת מוצרים: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    private func filterCustomers() {
        if customerSearchQuery.isEmpty {
            filteredCustomers = customers
        } else {
            filteredCustomers = customers.filter { customer in
                customer.nameHebrew.localizedCaseInsensitiveContains(customerSearchQuery) ||
                customer.businessNumber.contains(customerSearchQuery) ||
                (customer.contactEmail?.localizedCaseInsensitiveContains(customerSearchQuery) ?? false)
            }
        }
    }
    
    private func filterProducts() {
        if productSearchQuery.isEmpty {
            filteredProducts = products
        } else {
            filteredProducts = products.filter { product in
                product.nameHebrew.localizedCaseInsensitiveContains(productSearchQuery) ||
                (product.descriptionHebrew?.localizedCaseInsensitiveContains(productSearchQuery) ?? false) ||
                (product.sku?.localizedCaseInsensitiveContains(productSearchQuery) ?? false)
            }
        }
    }
    
    private func getCompanyId() -> String? {
        // Get the company ID from the selected company in AuthViewModel
        // This will be injected as an environment object
        print("📋 Getting company ID: \(selectedCompanyId ?? "nil")")
        return selectedCompanyId
    }

    // Company ID should be injected from the parent view
    var selectedCompanyId: String?

    func setCompanyId(_ companyId: String) {
        print("📋 Setting company ID in DocumentViewModel: \(companyId)")
        selectedCompanyId = companyId
    }

    // MARK: - Customer Creation Methods

    func createCustomer() {
        guard let companyId = getCompanyId() else {
            errorMessage = "שגיאה: לא נמצא מזהה חברה. אנא נסה לצאת ולהיכנס שוב לאפליקציה."
            print("❌ Company ID is nil when trying to create customer")
            print("❌ Selected company ID: \(selectedCompanyId ?? "nil")")
            return
        }

        guard customerFormData.isValid else {
            errorMessage = "יש למלא את כל השדות הנדרשים"
            return
        }

        isLoading = true
        errorMessage = nil

        Task {
            do {
                let request = customerFormData.toCreateRequest(companyId: companyId)
                let newCustomer = try await supabaseService.createCustomer(request)

                await MainActor.run {
                    self.customers.append(newCustomer)
                    self.selectedCustomer = newCustomer
                    self.showingCreateCustomer = false
                    self.customerFormData = CustomerFormData()
                    self.filterCustomers()
                    self.isLoading = false
                    self.successMessage = "לקוח נוצר בהצלחה"
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "שגיאה ביצירת לקוח: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }

    // MARK: - Product Creation Methods

    func createProduct() {
        guard let companyId = getCompanyId() else { return }
        guard productFormData.isValid else { return }

        isLoading = true
        errorMessage = nil

        Task {
            do {
                let request = productFormData.toCreateRequest(companyId: companyId)
                let newProduct = try await supabaseService.createProduct(request)

                await MainActor.run {
                    self.products.append(newProduct)
                    self.showingCreateProduct = false
                    self.productFormData = ProductFormData()
                    self.filterProducts()
                    self.isLoading = false
                    self.successMessage = "פריט נוצר בהצלחה"
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "שגיאה ביצירת פריט: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }

    // MARK: - Product Selection Methods

    func addProduct(_ product: Product) {
        var newItem = DocumentItemFormData()
        newItem.populateFromProduct(product)
        selectedProducts.append(newItem)
    }

    func removeProduct(at index: Int) {
        guard index < selectedProducts.count else { return }
        selectedProducts.remove(at: index)
    }

    func updateProductQuantity(at index: Int, quantity: String) {
        guard index < selectedProducts.count else { return }
        selectedProducts[index].quantity = quantity
    }

    func updateProductPrice(at index: Int, price: String) {
        guard index < selectedProducts.count else { return }
        selectedProducts[index].unitPrice = price
    }

    func updateProductDiscount(at index: Int, discount: String) {
        guard index < selectedProducts.count else { return }
        selectedProducts[index].discountPercent = discount
    }

    // MARK: - Document Creation Methods

    func createDocumentAsDraft() {
        createDocument(asDraft: true)
    }

    func createDocumentAndSend() {
        createDocument(asDraft: false)
    }

    private func createDocument(asDraft: Bool) {
        guard let companyId = getCompanyId(),
              let customer = selectedCustomer else { return }

        isLoading = true
        errorMessage = nil

        Task {
            do {
                // Create document items with global discount applied
                let items = selectedProducts.map { item -> CreateDocumentItemRequest in
                    var itemRequest = item.toCreateRequest()
                    // Apply global discount if any
                    if globalDiscountValue > 0 {
                        let currentDiscount = itemRequest.discountPercent ?? 0
                        let totalDiscount = min(100, currentDiscount + globalDiscountValue)
                        itemRequest = CreateDocumentItemRequest(
                            productId: itemRequest.productId,
                            descriptionHebrew: itemRequest.descriptionHebrew,
                            descriptionEnglish: itemRequest.descriptionEnglish,
                            quantity: itemRequest.quantity,
                            unitPrice: itemRequest.unitPrice,
                            vatRate: itemRequest.vatRate,
                            discountPercent: totalDiscount > 0 ? totalDiscount : nil
                        )
                    }
                    return itemRequest
                }

                let dateFormatter = DateFormatter()
                dateFormatter.dateFormat = "yyyy-MM-dd"

                let request = CreateDocumentRequest(
                    companyId: companyId,
                    documentType: selectedDocumentType,
                    customerId: customer.id,
                    issueDate: dateFormatter.string(from: issueDate),
                    dueDate: dueDate != nil ? dateFormatter.string(from: dueDate!) : nil,
                    currency: "ILS",
                    items: items,
                    notes: notes.isEmpty ? nil : notes,
                    templateId: "default"
                )

                let document = try await supabaseService.createDocument(request)

                // If not draft, send the document
                if !asDraft {
                    try await supabaseService.sendDocument(documentId: document.id, method: "email")
                }

                await MainActor.run {
                    self.createdDocument = document
                    self.showingDocumentSuccess = true
                    self.isLoading = false
                    self.successMessage = asDraft ? "טיוטה נשמרה בהצלחה" : "מסמך נוצר ונשלח בהצלחה"
                }
            } catch {
                await MainActor.run {
                    self.errorMessage = "שגיאה ביצירת מסמך: \(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }

    // MARK: - Document Sharing Methods

    func sendViaEmail() {
        guard MFMailComposeViewController.canSendMail() else {
            errorMessage = "לא ניתן לשלוח אימייל ממכשיר זה"
            return
        }
        showingMailComposer = true
    }

    func shareViaWhatsApp() {
        guard let document = createdDocument else { return }

        let message = getDocumentShareMessage()

        if let url = URL(string: "whatsapp://send?text=\(message.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")") {
            if UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url)
            } else {
                errorMessage = "וואטסאפ לא מותקן במכשיר"
            }
        }
    }

    func shareViaWhatsAppBusiness() {
        guard let document = createdDocument else { return }

        let message = getDocumentShareMessage()

        if let url = URL(string: "whatsapp-business://send?text=\(message.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? "")") {
            if UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url)
            } else {
                errorMessage = "וואטסאפ עסקי לא מותקן במכשיר"
            }
        }
    }

    func downloadPDF() {
        guard let document = createdDocument,
              let pdfUrl = document.pdfUrl,
              let url = URL(string: pdfUrl) else { return }

        // Open PDF URL in Safari or default browser
        UIApplication.shared.open(url)
    }

    private func getDocumentShareMessage() -> String {
        guard let document = createdDocument,
              let customer = selectedCustomer else {
            return "מסמך חדש נוצר"
        }

        let documentTypeName = selectedDocumentType.displayName
        let customerName = customer.displayName
        let total = document.formattedTotal

        var message = "שלום \(customerName),\n\n"
        message += "מצורף \(documentTypeName) מספר \(document.documentNumber)\n"
        message += "סכום: \(total)\n\n"

        if let pdfUrl = document.pdfUrl {
            message += "לצפייה במסמך: \(pdfUrl)\n\n"
        }

        message += "תודה!"

        return message
    }
}

// MARK: - Document Creation Steps
enum DocumentCreationStep: Int, CaseIterable {
    case documentType = 0
    case customerSelection = 1
    case productSelection = 2
    case summary = 3

    var title: String {
        switch self {
        case .documentType:
            return "סוג מסמך"
        case .customerSelection:
            return "בחירת לקוח"
        case .productSelection:
            return "בחירת מוצרים/שירותים"
        case .summary:
            return "סיכום"
        }
    }

    var stepNumber: Int {
        return rawValue + 1
    }

    static var totalSteps: Int {
        return DocumentCreationStep.allCases.count
    }
}
