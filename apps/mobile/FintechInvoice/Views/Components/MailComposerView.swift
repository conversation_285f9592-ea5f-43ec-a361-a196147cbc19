import SwiftUI
import MessageUI

struct MailComposerView: UIViewControllerRepresentable {
    let document: Document
    let customer: Customer
    @Binding var isPresented: <PERSON><PERSON>
    @Binding var result: Result<MFMailComposeResult, MailError>?
    
    func makeUIViewController(context: Context) -> MFMailComposeViewController {
        let composer = MFMailComposeViewController()
        composer.mailComposeDelegate = context.coordinator
        
        // Set email content
        composer.setSubject("מסמך \(document.documentType.displayName) מספר \(document.documentNumber)")
        
        if let email = customer.contactEmail {
            composer.setToRecipients([email])
        }
        
        let messageBody = """
        שלום \(customer.displayName),
        
        מצורף \(document.documentType.displayName) מספר \(document.documentNumber)
        בסך \(document.formattedTotal)
        
        תאריך הנפקה: \(document.formattedIssueDate)
        
        תודה רבה!
        """
        
        composer.setMessageBody(messageBody, isHTML: false)
        
        // If PDF URL is available, attach it
        if let pdfUrlString = document.pdfUrl,
           let pdfUrl = URL(string: pdfUrlString) {
            // In a real implementation, you would download the PDF and attach it
            // For now, we'll just include the link in the message
        }
        
        return composer
    }
    
    func updateUIViewController(_ uiViewController: MFMailComposeViewController, context: Context) {
        // No updates needed
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, MFMailComposeViewControllerDelegate {
        let parent: MailComposerView
        
        init(_ parent: MailComposerView) {
            self.parent = parent
        }
        
        func mailComposeController(_ controller: MFMailComposeViewController, didFinishWith result: MFMailComposeResult, error: Error?) {
            if let error = error {
                parent.result = .failure(.sendingFailed(error.localizedDescription))
            } else {
                parent.result = .success(result)
            }
            parent.isPresented = false
        }
    }
}

// MARK: - Mail Composer Button
struct MailComposerButton: View {
    let document: Document
    let customer: Customer
    @State private var showingMailComposer = false
    @State private var mailResult: Result<MFMailComposeResult, MailError>?
    
    var body: some View {
        Button(action: {
            if MFMailComposeViewController.canSendMail() {
                showingMailComposer = true
            }
        }) {
            HStack {
                Image(systemName: "envelope.fill")
                    .foregroundColor(.white)
                
                Text("שלח באימייל")
                    .font(.hebrewBody.weight(.medium))
                    .foregroundColor(.white)
            }
            .frame(maxWidth: .infinity)
            .padding(.spacing3)
            .background(Color.blue)
            .clipShape(RoundedRectangle(cornerRadius: .radiusMedium))
        }
        .disabled(!MFMailComposeViewController.canSendMail())
        .sheet(isPresented: $showingMailComposer) {
            MailComposerView(
                document: document,
                customer: customer,
                isPresented: $showingMailComposer,
                result: $mailResult
            )
        }
        .onChange(of: mailResult) { result in
            if let result = result {
                handleMailResult(result)
            }
        }
    }
    
    private func handleMailResult(_ result: Result<MFMailComposeResult, MailError>) {
        switch result {
        case .success(let mailResult):
            switch mailResult {
            case .sent:
                print("✅ Email sent successfully")
            case .saved:
                print("📝 Email saved as draft")
            case .cancelled:
                print("❌ Email cancelled")
            case .failed:
                print("❌ Email failed to send")
            @unknown default:
                print("❓ Unknown email result")
            }
        case .failure(let error):
            print("❌ Email error: \(error.localizedDescription)")
        }
    }
}

#Preview {
    // Preview with mock data
    let mockDocument = Document(
        id: "1",
        companyId: "company1",
        documentType: .taxInvoice,
        documentNumber: "INV-2025-001",
        customerId: "customer1",
        issueDate: "2025-01-15",
        dueDate: "2025-02-15",
        currency: "ILS",
        subtotal: 1000.0,
        vatAmount: 180.0,
        totalAmount: 1180.0,
        status: .sent,
        itaAllocationNumber: nil,
        itaAllocationDate: nil,
        itaSubmissionAttempts: 0,
        itaLastError: nil,
        parentDocumentId: nil,
        notes: nil,
        templateId: "default",
        pdfUrl: nil,
        sentAt: nil,
        sentVia: nil,
        createdBy: "user1",
        createdAt: "2025-01-15T10:00:00Z",
        updatedAt: "2025-01-15T10:00:00Z"
    )
    
    let mockCustomer = Customer(
        id: "1",
        companyId: "company1",
        businessNumber: "*********",
        nameHebrew: "חברת בדיקה בע״מ",
        nameEnglish: nil,
        vatId: "*********",
        billingAddressHebrew: "רחוב הבדיקה 123",
        billingAddressEnglish: nil,
        shippingAddressHebrew: nil,
        shippingAddressEnglish: nil,
        cityHebrew: "תל אביב",
        cityEnglish: nil,
        contactName: nil,
        contactEmail: "<EMAIL>",
        contactPhone: nil,
        notes: nil,
        createdAt: "2025-01-15T10:00:00Z",
        updatedAt: "2025-01-15T10:00:00Z"
    )
    
    MailComposerButton(document: mockDocument, customer: mockCustomer)
}
