import SwiftUI

struct EmailVerificationView: View {
    let email: String
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.dismiss) private var dismiss
    @State private var isResending = false
    @State private var resendMessage: String?
    
    var body: some View {
        VStack(spacing: .spacing8) {
            Spacer()
            
            // Email Icon
            Image(systemName: "envelope.circle.fill")
                .font(.system(size: 80))
                .foregroundColor(.primary)
                .padding(.bottom, .spacing4)
            
            // Title
            Text("אמת את כתובת האימייל שלך")
                .font(.hebrewTitle.weight(.bold))
                .foregroundColor(.cardForeground)
                .multilineTextAlignment(.center)
                .padding(.bottom, .spacing2)
            
            // Description
            VStack(spacing: .spacing3) {
                Text("שלחנו לך אימייל לכתובת:")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
                
                Text(email)
                    .font(.hebrewBody.weight(.medium))
                    .foregroundColor(.cardForeground)
                    .padding(.horizontal, .spacing4)
                    .padding(.vertical, .spacing2)
                    .background(Color.inputBackground)
                    .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
                
                Text("לחץ על הקישור באימייל כדי לאמת את החשבון שלך")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, .spacing4)
            }
            .padding(.bottom, .spacing6)
            
            // Instructions
            VStack(alignment: .trailing, spacing: .spacing3) {
                HStack {
                    Image(systemName: "1.circle.fill")
                        .foregroundColor(.primary)
                    Spacer()
                    Text("בדוק את תיבת הדואר הנכנס שלך")
                        .font(.hebrewCaption)
                        .foregroundColor(.cardForeground)
                }
                
                HStack {
                    Image(systemName: "2.circle.fill")
                        .foregroundColor(.primary)
                    Spacer()
                    Text("לחץ על הקישור באימייל")
                        .font(.hebrewCaption)
                        .foregroundColor(.cardForeground)
                }
                
                HStack {
                    Image(systemName: "3.circle.fill")
                        .foregroundColor(.primary)
                    Spacer()
                    Text("חזור לאפליקציה והתחבר")
                        .font(.hebrewCaption)
                        .foregroundColor(.cardForeground)
                }
            }
            .padding(.horizontal, .spacing4)
            .padding(.vertical, .spacing4)
            .background(Color.card)
            .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
            .padding(.bottom, .spacing6)
            
            // Resend Email Button
            Button(action: resendEmail) {
                HStack {
                    if isResending {
                        ProgressView()
                            .scaleEffect(0.8)
                            .tint(.primaryForeground)
                    }
                    Text(isResending ? "שולח..." : "שלח אימייל שוב")
                        .font(.hebrewBody)
                        .fontWeight(.medium)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, .spacing3)
                .background(Color.secondary)
                .foregroundColor(.secondaryForeground)
                .clipShape(RoundedRectangle(cornerRadius: .radiusDefault))
            }
            .disabled(isResending)
            
            // Resend message
            if let message = resendMessage {
                Text(message)
                    .font(.hebrewCaption)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.center)
                    .padding(.top, .spacing2)
            }
            
            Spacer()
            
            // Back to Login Button
            Button(action: {
                dismiss()
            }) {
                Text("חזור להתחברות")
                    .font(.hebrewBody)
                    .foregroundColor(.primary)
            }
            .padding(.bottom, .spacing4)
        }
        .padding(.horizontal, .spacing6)
        .background(Color.background)
        .navigationBarHidden(true)
    }
    
    private func resendEmail() {
        isResending = true
        resendMessage = nil

        Task {
            do {
                try await SupabaseService.shared.resendConfirmationEmail(email: email)

                await MainActor.run {
                    resendMessage = "אימייל נשלח שוב בהצלחה"
                    isResending = false
                }
            } catch {
                await MainActor.run {
                    resendMessage = "שגיאה בשליחת האימייל. נסה שוב מאוחר יותר"
                    isResending = false
                }
            }
        }
    }
}

#Preview {
    EmailVerificationView(email: "<EMAIL>")
        .environmentObject(AuthViewModel())
}
