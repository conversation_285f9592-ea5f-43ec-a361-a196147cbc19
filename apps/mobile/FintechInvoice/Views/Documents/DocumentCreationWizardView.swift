import SwiftUI

struct DocumentCreationWizardView: View {
    @EnvironmentObject var authViewModel: AuthViewModel
    @Environment(\.dismiss) private var dismiss
    @StateObject private var documentViewModel: DocumentViewModel

    init() {
        // We need to initialize the StateObject with a company ID
        // This will be set properly when the view appears
        self._documentViewModel = StateObject(wrappedValue: DocumentViewModel())
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // Progress Bar
                ProgressBarView(
                    currentStep: documentViewModel.currentStep.stepNumber,
                    totalSteps: DocumentCreationStep.totalSteps
                )
                .padding(.horizontal, .spacing4)
                .padding(.top, .spacing2)
                
                // Content
                ScrollView {
                    VStack(spacing: .spacing6) {
                        currentStepView
                    }
                    .padding(.spacing4)
                }
                .background(Color.background)
                
                // Navigation Buttons
                NavigationButtonsView(documentViewModel: documentViewModel)
            }
            .background(Color.background)
            .navigationTitle(documentViewModel.currentStep.title)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("ביטול") {
                        dismiss()
                    }
                    .foregroundColor(.primary)
                }
            }
        }
        .environment(\.layoutDirection, .rightToLeft)
        .onAppear {
            // Set the company ID from the selected company
            print("📋 DocumentCreationWizardView onAppear - Selected company: \(authViewModel.selectedCompany?.id ?? "nil")")
            if let selectedCompany = authViewModel.selectedCompany {
                documentViewModel.setCompanyId(selectedCompany.id)
                // Also load initial data that depends on company ID
                documentViewModel.loadCustomers()
                documentViewModel.loadProducts()
            } else {
                print("❌ No selected company found in authViewModel")
            }
        }
        .sheet(isPresented: $documentViewModel.showingCreateCustomer) {
            CreateCustomerView(documentViewModel: documentViewModel)
        }
        .sheet(isPresented: $documentViewModel.showingCreateProduct) {
            CreateProductView(documentViewModel: documentViewModel)
        }
        .sheet(isPresented: $documentViewModel.showingDocumentSuccess) {
            DocumentSuccessView(documentViewModel: documentViewModel)
        }
        .alert("שגיאה", isPresented: .constant(documentViewModel.errorMessage != nil)) {
            Button("אישור") {
                documentViewModel.errorMessage = nil
            }
        } message: {
            Text(documentViewModel.errorMessage ?? "")
        }
        .alert("הצלחה", isPresented: .constant(documentViewModel.successMessage != nil)) {
            Button("אישור") {
                documentViewModel.successMessage = nil
            }
        } message: {
            Text(documentViewModel.successMessage ?? "")
        }
    }
    
    @ViewBuilder
    private var currentStepView: some View {
        switch documentViewModel.currentStep {
        case .documentType:
            DocumentTypeSelectionView(documentViewModel: documentViewModel)
        case .customerSelection:
            CustomerSelectionView(documentViewModel: documentViewModel)
        case .productSelection:
            ProductSelectionView(documentViewModel: documentViewModel)
        case .summary:
            DocumentSummaryView(documentViewModel: documentViewModel)
        }
    }
}

// MARK: - Progress Bar View
struct ProgressBarView: View {
    let currentStep: Int
    let totalSteps: Int
    
    private var progress: Double {
        return Double(currentStep) / Double(totalSteps)
    }
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing2) {
            HStack {
                Text("שלב \(currentStep) מתוך \(totalSteps)")
                    .font(.hebrewCaption)
                    .foregroundColor(.mutedForeground)
                
                Spacer()
            }
            
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(Color.muted.opacity(0.3))
                        .frame(height: 4)
                        .clipShape(RoundedRectangle(cornerRadius: 2))
                    
                    Rectangle()
                        .fill(Color.primary)
                        .frame(width: geometry.size.width * progress, height: 4)
                        .clipShape(RoundedRectangle(cornerRadius: 2))
                        .animation(.easeInOut(duration: 0.3), value: progress)
                }
            }
            .frame(height: 4)
        }
    }
}

// MARK: - Navigation Buttons View
struct NavigationButtonsView: View {
    @ObservedObject var documentViewModel: DocumentViewModel
    
    var body: some View {
        VStack(spacing: .spacing3) {
            if documentViewModel.isLoading {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle())
                    .scaleEffect(1.2)
                    .padding(.spacing4)
            } else {
                HStack(spacing: .spacing4) {
                    // Back Button
                    if documentViewModel.canGoBack {
                        Button("חזור") {
                            documentViewModel.goToPreviousStep()
                        }
                        .buttonStyle(CosmicSecondaryButtonStyle())
                        .frame(maxWidth: .infinity)
                    }
                    
                    // Next/Create Button
                    if documentViewModel.currentStep == .summary {
                        HStack(spacing: .spacing3) {
                            Button("שמור כטיוטה") {
                                documentViewModel.createDocumentAsDraft()
                            }
                            .buttonStyle(CosmicSecondaryButtonStyle())
                            .frame(maxWidth: .infinity)
                            
                            Button("צור מסמך") {
                                documentViewModel.createDocumentAndSend()
                            }
                            .buttonStyle(CosmicPrimaryButtonStyle())
                            .frame(maxWidth: .infinity)
                        }
                    } else {
                        Button("המשך") {
                            documentViewModel.goToNextStep()
                        }
                        .buttonStyle(CosmicPrimaryButtonStyle())
                        .frame(maxWidth: .infinity)
                        .disabled(!documentViewModel.canProceedToNextStep)
                    }
                }
                .padding(.horizontal, .spacing4)
                .padding(.vertical, .spacing3)
            }
        }
        .background(Color.card)
        .overlay(
            Rectangle()
                .frame(height: 1)
                .foregroundColor(Color.border),
            alignment: .top
        )
    }
}

// MARK: - Document Type Selection View
struct DocumentTypeSelectionView: View {
    @ObservedObject var documentViewModel: DocumentViewModel
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing6) {
            VStack(alignment: .trailing, spacing: .spacing3) {
                Text("בחר סוג מסמך")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                Text("בחר את סוג המסמך שברצונך ליצור")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.trailing)
            }
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: .spacing4) {
                ForEach(DocumentType.allCases, id: \.self) { documentType in
                    DocumentTypeCard(
                        documentType: documentType,
                        isSelected: documentViewModel.selectedDocumentType == documentType
                    ) {
                        documentViewModel.selectedDocumentType = documentType
                    }
                }
            }
        }
    }
}

// MARK: - Document Type Card
struct DocumentTypeCard: View {
    let documentType: DocumentType
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: .spacing4) {
                Image(systemName: documentType.icon)
                    .font(.system(size: 32))
                    .foregroundColor(isSelected ? .primary : .mutedForeground)
                
                Text(documentType.displayName)
                    .font(.hebrewBody.weight(.medium))
                    .foregroundColor(isSelected ? .cardForeground : .mutedForeground)
                    .multilineTextAlignment(.center)
            }
            .frame(maxWidth: .infinity)
            .padding(.spacing4)
            .background(
                RoundedRectangle(cornerRadius: .radiusMedium)
                    .fill(isSelected ? Color.primary.opacity(0.1) : Color.muted.opacity(0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: .radiusMedium)
                            .stroke(isSelected ? Color.primary : Color.clear, lineWidth: 2)
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    DocumentCreationWizardView()
}
