import SwiftUI

struct DocumentSummaryView: View {
    @ObservedObject var documentViewModel: DocumentViewModel
    
    var body: some View {
        VStack(alignment: .trailing, spacing: .spacing6) {
            // Header
            VStack(alignment: .trailing, spacing: .spacing3) {
                Text("סיכום המסמך")
                    .font(.hebrewHeading.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                Text("בדוק את פרטי המסמך לפני יצירתו")
                    .font(.hebrewBody)
                    .foregroundColor(.mutedForeground)
                    .multilineTextAlignment(.trailing)
            }
            
            // Document Info Card
            VStack(alignment: .trailing, spacing: .spacing4) {
                HStack {
                    Text(documentViewModel.selectedDocumentType.displayName)
                        .font(.hebrewBody.weight(.semibold))
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    Text("סוג מסמך:")
                        .font(.hebrewBody.weight(.medium))
                        .foregroundColor(.cardForeground)
                }
                
                if let customer = documentViewModel.selectedCustomer {
                    VStack(alignment: .trailing, spacing: .spacing2) {
                        HStack {
                            VStack(alignment: .leading, spacing: .spacing1) {
                                Text(customer.displayName)
                                    .font(.hebrewBody.weight(.medium))
                                    .foregroundColor(.cardForeground)
                                
                                Text(customer.fullAddress)
                                    .font(.caption2)
                                    .foregroundColor(.mutedForeground)
                                
                                if let vatId = customer.formattedVatId {
                                    Text("ע.מ: \(vatId)")
                                        .font(.caption2)
                                        .foregroundColor(.mutedForeground)
                                }
                            }
                            
                            Spacer()
                            
                            Text("לקוח:")
                                .font(.hebrewBody.weight(.medium))
                                .foregroundColor(.cardForeground)
                        }
                    }
                }
                
                // Dates
                VStack(spacing: .spacing2) {
                    HStack {
                        Text(DateFormatter.displayFormatter.string(from: documentViewModel.issueDate))
                            .font(.hebrewBody)
                            .foregroundColor(.cardForeground)
                        
                        Spacer()
                        
                        Text("תאריך הנפקה:")
                            .font(.hebrewBody.weight(.medium))
                            .foregroundColor(.cardForeground)
                    }
                    
                    if let dueDate = documentViewModel.dueDate {
                        HStack {
                            Text(DateFormatter.displayFormatter.string(from: dueDate))
                                .font(.hebrewBody)
                                .foregroundColor(.cardForeground)
                            
                            Spacer()
                            
                            Text("תאריך פירעון:")
                                .font(.hebrewBody.weight(.medium))
                                .foregroundColor(.cardForeground)
                        }
                    }
                }
            }
            .padding(.spacing4)
            .cosmicCard()
            
            // Items List
            VStack(alignment: .trailing, spacing: .spacing3) {
                Text("פריטים (\(documentViewModel.selectedProducts.count))")
                    .font(.hebrewBody.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                LazyVStack(spacing: .spacing3) {
                    ForEach(Array(documentViewModel.selectedProducts.enumerated()), id: \.element.id) { index, item in
                        SummaryItemRowView(item: item, index: index + 1)
                    }
                }
            }
            .padding(.spacing4)
            .cosmicCard()
            
            // Global Discount
            VStack(alignment: .trailing, spacing: .spacing3) {
                Text("הנחה כללית")
                    .font(.hebrewBody.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                HStack {
                    Text("%")
                        .font(.hebrewBody)
                        .foregroundColor(.mutedForeground)
                    
                    TextField("0", text: $documentViewModel.globalDiscountPercent)
                        .textFieldStyle(CosmicTextFieldStyle())
                        .keyboardType(.decimalPad)
                        .frame(width: 80)
                        .multilineTextAlignment(.trailing)
                    
                    Spacer()
                    
                    Text("הנחה כללית על כל הפריטים:")
                        .font(.hebrewBody)
                        .foregroundColor(.cardForeground)
                }
            }
            .padding(.spacing4)
            .cosmicCard()
            
            // Totals Summary
            VStack(alignment: .trailing, spacing: .spacing3) {
                Text("סיכום כספי")
                    .font(.hebrewBody.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                VStack(spacing: .spacing2) {
                    HStack {
                        Text(documentViewModel.documentSummary.formattedSubtotal)
                            .font(.hebrewBody)
                            .foregroundColor(.cardForeground)
                        
                        Spacer()
                        
                        Text("סה״כ לפני מע״מ:")
                            .font(.hebrewBody)
                            .foregroundColor(.cardForeground)
                    }
                    
                    if documentViewModel.globalDiscountValue > 0 {
                        HStack {
                            Text("-₪\(String(format: "%.2f", documentViewModel.documentSummary.subtotal * (documentViewModel.globalDiscountValue / 100)))")
                                .font(.hebrewBody)
                                .foregroundColor(.destructive)
                            
                            Spacer()
                            
                            Text("הנחה כללית (\(String(format: "%.1f", documentViewModel.globalDiscountValue))%):")
                                .font(.hebrewBody)
                                .foregroundColor(.cardForeground)
                        }
                        
                        HStack {
                            Text(documentViewModel.formattedFinalSubtotal)
                                .font(.hebrewBody)
                                .foregroundColor(.cardForeground)
                            
                            Spacer()
                            
                            Text("סה״כ אחרי הנחה:")
                                .font(.hebrewBody)
                                .foregroundColor(.cardForeground)
                        }
                    }
                    
                    HStack {
                        Text(documentViewModel.formattedFinalVat)
                            .font(.hebrewBody)
                            .foregroundColor(.cardForeground)
                        
                        Spacer()
                        
                        Text("מע״מ:")
                            .font(.hebrewBody)
                            .foregroundColor(.cardForeground)
                    }
                    
                    Divider()
                    
                    HStack {
                        Text(documentViewModel.formattedFinalTotal)
                            .font(.hebrewHeading.weight(.bold))
                            .foregroundColor(.primary)
                        
                        Spacer()
                        
                        Text("סה״כ לתשלום:")
                            .font(.hebrewHeading.weight(.semibold))
                            .foregroundColor(.cardForeground)
                    }
                }
            }
            .padding(.spacing4)
            .cosmicCard()
            
            // Notes
            VStack(alignment: .trailing, spacing: .spacing3) {
                Text("הערות")
                    .font(.hebrewBody.weight(.semibold))
                    .foregroundColor(.cardForeground)
                
                if #available(iOS 16.0, *) {
                    TextField("הערות נוספות למסמך...", text: $documentViewModel.notes, axis: .vertical)
                        .textFieldStyle(CosmicTextFieldStyle())
                        .multilineTextAlignment(.trailing)
                        .lineLimit(3...6)
                } else {
                    TextField("הערות נוספות למסמך...", text: $documentViewModel.notes)
                        .textFieldStyle(CosmicTextFieldStyle())
                        .multilineTextAlignment(.trailing)
                        .lineLimit(3)
                }
            }
            .padding(.spacing4)
            .cosmicCard()
        }
    }
}

// MARK: - Summary Item Row View
struct SummaryItemRowView: View {
    let item: DocumentItemFormData
    let index: Int
    
    var body: some View {
        VStack(spacing: .spacing2) {
            HStack {
                VStack(alignment: .leading, spacing: .spacing1) {
                    Text("\(index).")
                        .font(.caption2)
                        .foregroundColor(.mutedForeground)
                    
                    if Double(item.discountPercent) ?? 0.0 > 0 {
                        Text("הנחה: \(item.discountPercent)%")
                            .font(.caption2)
                            .foregroundColor(.destructive)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: .spacing1) {
                    Text(item.descriptionHebrew)
                        .font(.hebrewBody.weight(.medium))
                        .foregroundColor(.cardForeground)
                        .multilineTextAlignment(.trailing)
                    
                    HStack(spacing: .spacing2) {
                        Text(item.formattedTotalWithVat)
                            .font(.hebrewCaption.weight(.semibold))
                            .foregroundColor(.primary)
                        
                        Text("×")
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                        
                        Text("כמות: \(item.quantity)")
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                        
                        Text("×")
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                        
                        Text("מחיר יחידה: ₪\(item.unitPrice)")
                            .font(.caption2)
                            .foregroundColor(.mutedForeground)
                    }
                }
            }
        }
        .padding(.spacing3)
        .background(Color.muted.opacity(0.3))
        .clipShape(RoundedRectangle(cornerRadius: .radiusSmall))
    }
}

// MARK: - Date Formatter Extension
extension DateFormatter {
    static let displayFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "he_IL")
        return formatter
    }()
}

#Preview {
    DocumentSummaryView(documentViewModel: DocumentViewModel())
}
