import Foundation

// MARK: - Document Item Model
struct DocumentItem: Identifiable, Codable {
    let id: String
    let documentId: String
    let productId: String?
    let lineNumber: Int
    let descriptionHebrew: String
    let descriptionEnglish: String?
    let quantity: Double
    let unitPrice: Double
    let currency: String
    let discountPercent: Double
    let vatRate: Double
    let lineTotal: Double
    let vatAmount: Double
    let totalWithVat: Double
    
    // Computed properties for display
    var formattedQuantity: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.maximumFractionDigits = 3
        formatter.minimumFractionDigits = 0
        return formatter.string(from: NSNumber(value: quantity)) ?? "\(quantity)"
    }
    
    var formattedUnitPrice: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: unitPrice)) ?? "₪\(unitPrice)"
    }
    
    var formattedLineTotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: lineTotal)) ?? "₪\(lineTotal)"
    }
    
    var formattedVatAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: vatAmount)) ?? "₪\(vatAmount)"
    }
    
    var formattedTotalWithVat: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: totalWithVat)) ?? "₪\(totalWithVat)"
    }
    
    var formattedVatRate: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .percent
        formatter.maximumFractionDigits = 1
        return formatter.string(from: NSNumber(value: vatRate / 100)) ?? "\(vatRate)%"
    }
    
    var formattedDiscountPercent: String {
        if discountPercent > 0 {
            let formatter = NumberFormatter()
            formatter.numberStyle = .percent
            formatter.maximumFractionDigits = 1
            return formatter.string(from: NSNumber(value: discountPercent / 100)) ?? "\(discountPercent)%"
        }
        return ""
    }
    
    var hasDiscount: Bool {
        return discountPercent > 0
    }
    
    // Coding keys to map Swift property names to database field names
    enum CodingKeys: String, CodingKey {
        case id
        case documentId = "document_id"
        case productId = "product_id"
        case lineNumber = "line_number"
        case descriptionHebrew = "description_hebrew"
        case descriptionEnglish = "description_english"
        case quantity
        case unitPrice = "unit_price"
        case currency
        case discountPercent = "discount_percent"
        case vatRate = "vat_rate"
        case lineTotal = "line_total"
        case vatAmount = "vat_amount"
        case totalWithVat = "total_with_vat"
    }
}

// MARK: - Document Item Form Data (for UI)
struct DocumentItemFormData: Identifiable {
    let id = UUID()
    var selectedProduct: Product?
    var descriptionHebrew: String = ""
    var quantity: String = "1"
    var unitPrice: String = ""
    var vatRate: Double = 18.0
    var discountPercent: String = "0"
    
    var isValid: Bool {
        return !descriptionHebrew.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
               quantityValue > 0 &&
               unitPriceValue > 0
    }
    
    var quantityValue: Double {
        return Double(quantity.replacingOccurrences(of: ",", with: ".")) ?? 0.0
    }
    
    var unitPriceValue: Double {
        return Double(unitPrice.replacingOccurrences(of: ",", with: ".")) ?? 0.0
    }
    
    var discountPercentValue: Double {
        let value = Double(discountPercent.replacingOccurrences(of: ",", with: ".")) ?? 0.0
        return max(0, min(100, value)) // Clamp between 0 and 100
    }
    
    // Calculated totals
    var subtotal: Double {
        return quantityValue * unitPriceValue
    }
    
    var discountAmount: Double {
        return subtotal * (discountPercentValue / 100)
    }
    
    var lineTotal: Double {
        return subtotal - discountAmount
    }
    
    var vatAmount: Double {
        return lineTotal * (vatRate / 100)
    }
    
    var totalWithVat: Double {
        return lineTotal + vatAmount
    }
    
    // Formatted display values
    var formattedSubtotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: subtotal)) ?? "₪\(subtotal)"
    }
    
    var formattedLineTotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: lineTotal)) ?? "₪\(lineTotal)"
    }
    
    var formattedVatAmount: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: vatAmount)) ?? "₪\(vatAmount)"
    }
    
    var formattedTotalWithVat: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: totalWithVat)) ?? "₪\(totalWithVat)"
    }
    
    // Validation errors
    var descriptionError: String? {
        if descriptionHebrew.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return "תיאור הוא שדה חובה"
        }
        return nil
    }
    
    var quantityError: String? {
        if quantity.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return "כמות היא שדה חובה"
        }
        if quantityValue <= 0 {
            return "כמות חייבת להיות גדולה מאפס"
        }
        return nil
    }
    
    var unitPriceError: String? {
        if unitPrice.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return "מחיר יחידה הוא שדה חובה"
        }
        if unitPriceValue <= 0 {
            return "מחיר יחידה חייב להיות גדול מאפס"
        }
        return nil
    }
    
    var discountError: String? {
        if discountPercentValue < 0 || discountPercentValue > 100 {
            return "הנחה חייבת להיות בין 0 ל-100 אחוז"
        }
        return nil
    }
    
    // Initialize from product
    mutating func populateFromProduct(_ product: Product) {
        selectedProduct = product
        descriptionHebrew = product.displayName
        unitPrice = String(product.unitPrice)
        vatRate = product.vatRate
    }
    
    // Convert to create request format
    func toCreateRequest() -> CreateDocumentItemRequest {
        return CreateDocumentItemRequest(
            productId: selectedProduct?.id,
            descriptionHebrew: descriptionHebrew.trimmingCharacters(in: .whitespacesAndNewlines),
            descriptionEnglish: nil,
            quantity: quantityValue,
            unitPrice: unitPriceValue,
            vatRate: vatRate,
            discountPercent: discountPercentValue > 0 ? discountPercentValue : nil
        )
    }
}

// MARK: - Document Items Summary
struct DocumentItemsSummary {
    let items: [DocumentItemFormData]
    
    var subtotal: Double {
        return items.reduce(0) { $0 + $1.lineTotal }
    }
    
    var totalVat: Double {
        return items.reduce(0) { $0 + $1.vatAmount }
    }
    
    var total: Double {
        return items.reduce(0) { $0 + $1.totalWithVat }
    }
    
    var formattedSubtotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: subtotal)) ?? "₪\(subtotal)"
    }
    
    var formattedTotalVat: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: totalVat)) ?? "₪\(totalVat)"
    }
    
    var formattedTotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = "ILS"
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: total)) ?? "₪\(total)"
    }
}
