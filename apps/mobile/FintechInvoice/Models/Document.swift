import Foundation

// MARK: - Document Types and Status Enums
enum DocumentType: String, CaseIterable, Codable {
    case taxInvoice = "tax_invoice"
    case receipt = "receipt"
    case creditNote = "credit_note"
    case taxInvoiceReceipt = "tax_invoice_receipt"
    
    var displayName: String {
        switch self {
        case .taxInvoice:
            return "חשבונית מס"
        case .receipt:
            return "קבלה"
        case .creditNote:
            return "זיכוי"
        case .taxInvoiceReceipt:
            return "חשבונית מס/קבלה"
        }
    }
    
    var icon: String {
        switch self {
        case .taxInvoice:
            return "doc.text"
        case .receipt:
            return "receipt"
        case .creditNote:
            return "minus.circle"
        case .taxInvoiceReceipt:
            return "doc.text.fill"
        }
    }
}

enum DocumentStatus: String, CaseIterable, Codable {
    case draft = "draft"
    case pendingAllocation = "pending_allocation"
    case approved = "approved"
    case sent = "sent"
    case paid = "paid"
    case cancelled = "cancelled"
    
    var displayName: String {
        switch self {
        case .draft:
            return "טיוטה"
        case .pendingAllocation:
            return "ממתין להקצאה"
        case .approved:
            return "מאושר"
        case .sent:
            return "נשלח"
        case .paid:
            return "שולם"
        case .cancelled:
            return "בוטל"
        }
    }
    
    var color: String {
        switch self {
        case .draft:
            return "gray"
        case .pendingAllocation:
            return "orange"
        case .approved:
            return "blue"
        case .sent:
            return "purple"
        case .paid:
            return "green"
        case .cancelled:
            return "red"
        }
    }
}

// MARK: - Document Model
struct Document: Identifiable, Codable {
    let id: String
    let companyId: String
    let documentType: DocumentType
    let documentNumber: String
    let customerId: String
    let issueDate: String
    let dueDate: String?
    let currency: String
    let subtotal: Double
    let vatAmount: Double
    let totalAmount: Double
    let status: DocumentStatus
    let itaAllocationNumber: String?
    let itaAllocationDate: String?
    let itaSubmissionAttempts: Int
    let itaLastError: String?
    let parentDocumentId: String?
    let notes: String?
    let templateId: String
    let pdfUrl: String?
    let sentAt: String?
    let sentVia: String?
    let createdBy: String
    let createdAt: String
    let updatedAt: String
    
    // Computed properties for display
    var formattedTotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: totalAmount)) ?? "₪\(totalAmount)"
    }
    
    var formattedSubtotal: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: subtotal)) ?? "₪\(subtotal)"
    }
    
    var formattedVat: String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.currencyCode = currency
        formatter.currencySymbol = "₪"
        return formatter.string(from: NSNumber(value: vatAmount)) ?? "₪\(vatAmount)"
    }
    
    var formattedIssueDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "he_IL")
        
        let isoFormatter = DateFormatter()
        isoFormatter.dateFormat = "yyyy-MM-dd"
        
        if let date = isoFormatter.date(from: issueDate) {
            return formatter.string(from: date)
        }
        return issueDate
    }
    
    var formattedDueDate: String? {
        guard let dueDate = dueDate else { return nil }
        
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.locale = Locale(identifier: "he_IL")
        
        let isoFormatter = DateFormatter()
        isoFormatter.dateFormat = "yyyy-MM-dd"
        
        if let date = isoFormatter.date(from: dueDate) {
            return formatter.string(from: date)
        }
        return dueDate
    }
    
    // Coding keys to map Swift property names to database field names
    enum CodingKeys: String, CodingKey {
        case id
        case companyId = "company_id"
        case documentType = "document_type"
        case documentNumber = "document_number"
        case customerId = "customer_id"
        case issueDate = "issue_date"
        case dueDate = "due_date"
        case currency
        case subtotal
        case vatAmount = "vat_amount"
        case totalAmount = "total_amount"
        case status
        case itaAllocationNumber = "ita_allocation_number"
        case itaAllocationDate = "ita_allocation_date"
        case itaSubmissionAttempts = "ita_submission_attempts"
        case itaLastError = "ita_last_error"
        case parentDocumentId = "parent_document_id"
        case notes
        case templateId = "template_id"
        case pdfUrl = "pdf_url"
        case sentAt = "sent_at"
        case sentVia = "sent_via"
        case createdBy = "created_by"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

// MARK: - Document Creation Request
struct CreateDocumentRequest: Codable {
    let companyId: String
    let documentType: DocumentType
    let customerId: String
    let issueDate: String
    let dueDate: String?
    let currency: String
    let items: [CreateDocumentItemRequest]
    let notes: String?
    let templateId: String?
    
    enum CodingKeys: String, CodingKey {
        case companyId = "company_id"
        case documentType = "document_type"
        case customerId = "customer_id"
        case issueDate = "issue_date"
        case dueDate = "due_date"
        case currency
        case items
        case notes
        case templateId = "template_id"
    }
}

struct CreateDocumentItemRequest: Codable {
    let productId: String?
    let descriptionHebrew: String
    let descriptionEnglish: String?
    let quantity: Double
    let unitPrice: Double
    let vatRate: Double
    let discountPercent: Double?
    
    enum CodingKeys: String, CodingKey {
        case productId = "product_id"
        case descriptionHebrew = "description_hebrew"
        case descriptionEnglish = "description_english"
        case quantity
        case unitPrice = "unit_price"
        case vatRate = "vat_rate"
        case discountPercent = "discount_percent"
    }
}

// MARK: - Document Response Types
struct DocumentResponse: Codable {
    let success: Bool
    let data: DocumentResponseData?
    let error: String?
}

struct DocumentResponseData: Codable {
    let document: Document
    let items: [DocumentItem]?
}

struct NextDocumentNumberResponse: Codable {
    let success: Bool
    let data: NextDocumentNumberData?
    let error: String?
}

struct NextDocumentNumberData: Codable {
    let nextNumber: String
    let prefix: String
    let sequence: Int
    
    enum CodingKeys: String, CodingKey {
        case nextNumber = "next_number"
        case prefix
        case sequence
    }
}
