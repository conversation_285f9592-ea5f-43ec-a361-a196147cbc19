// Shared utilities for all Supabase Edge Functions

import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { ApiResponse, ValidationError } from './types.ts';

// Initialize Supabase client
export function createSupabaseClient() {
  const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
  const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;
  
  return createClient(supabaseUrl, supabaseServiceKey);
}

// CORS headers for all responses
export const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'GET, POST, PUT, PATCH, DELETE, OPTIONS',
};

// Standard error response
export function errorResponse(message: string, status = 400): Response {
  return new Response(
    JSON.stringify({
      success: false,
      error: message,
    } as ApiResponse),
    {
      status,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  );
}

// Standard success response
export function successResponse<T>(data: T, message?: string): Response {
  return new Response(
    JSON.stringify({
      success: true,
      data,
      message,
    } as ApiResponse<T>),
    {
      status: 200,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  );
}

// Validation error response
export function validationErrorResponse(errors: ValidationError[]): Response {
  return new Response(
    JSON.stringify({
      success: false,
      error: 'Validation failed',
      validation_errors: errors,
    }),
    {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    }
  );
}

// Handle CORS preflight
export function handleCors(req: Request): Response | null {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }
  return null;
}

// Validate Israeli business number (9 digits)
export function validateBusinessNumber(businessNumber: string): boolean {
  const regex = /^[0-9]{9}$/;
  return regex.test(businessNumber);
}

// Validate Israeli VAT ID (9 digits)
export function validateVatId(vatId: string): boolean {
  const regex = /^[0-9]{9}$/;
  return regex.test(vatId);
}

// Validate Israeli phone number
export function validateIsraeliPhone(phone: string): boolean {
  const regex = /^(\+972|0)(5[0-9]|7[1-9])[0-9]{7}$/;
  return regex.test(phone);
}

// Validate email format
export function validateEmail(email: string): boolean {
  const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return regex.test(email);
}

// Validate password strength
export function validatePassword(password: string): boolean {
  // At least 8 characters, 1 uppercase, 1 number
  const regex = /^(?=.*[A-Z])(?=.*\d).{8,}$/;
  return regex.test(password);
}

// Validate amount format
export function validateAmount(amount: string | number): boolean {
  const regex = /^\d+(\.\d{1,2})?$/;
  return regex.test(amount.toString());
}

// Get user from JWT token
export async function getUserFromToken(req: Request) {
  const authHeader = req.headers.get('authorization');
  if (!authHeader) {
    throw new Error('No authorization header');
  }

  const token = authHeader.replace('Bearer ', '');
  const supabase = createSupabaseClient();
  
  const { data: { user }, error } = await supabase.auth.getUser(token);
  
  if (error || !user) {
    throw new Error('Invalid token');
  }
  
  return user;
}

// Get user's company access
export async function getUserCompanyAccess(userId: string, companyId?: string) {
  const supabase = createSupabaseClient();
  
  let query = supabase
    .from('company_users')
    .select(`
      *,
      company:companies(*)
    `)
    .eq('user_id', userId);
    
  if (companyId) {
    query = query.eq('company_id', companyId);
  }
  
  const { data, error } = await query;
  
  if (error) {
    throw new Error('Failed to get company access');
  }
  
  return data;
}

// Check if user has role in company
export async function checkUserRole(userId: string, companyId: string, requiredRoles: string[]) {
  const access = await getUserCompanyAccess(userId, companyId);
  
  if (!access || access.length === 0) {
    throw new Error('No access to company');
  }
  
  const userRole = access[0].role;
  if (!requiredRoles.includes(userRole)) {
    throw new Error('Insufficient permissions');
  }
  
  return access[0];
}

// Generate next document number
export async function getNextDocumentNumber(companyId: string, documentType: string) {
  const supabase = createSupabaseClient();
  
  // Use a transaction to ensure atomicity
  const { data, error } = await supabase.rpc('get_next_document_number', {
    p_company_id: companyId,
    p_document_type: documentType
  });
  
  if (error) {
    throw new Error('Failed to generate document number');
  }
  
  return data;
}

// Calculate document totals
export function calculateDocumentTotals(items: any[]) {
  let subtotal = 0;
  let totalDiscount = 0;
  let vatAmount = 0;
  
  const calculatedItems = items.map((item, index) => {
    const baseAmount = item.quantity * item.unit_price;
    const discountAmount = baseAmount * (item.discount_percent || 0) / 100;
    const lineTotal = baseAmount - discountAmount;
    const itemVatAmount = lineTotal * (item.vat_rate || 18) / 100;
    const totalWithVat = lineTotal + itemVatAmount;
    
    subtotal += lineTotal;
    totalDiscount += discountAmount;
    vatAmount += itemVatAmount;
    
    return {
      ...item,
      line_number: index + 1,
      line_total: Math.round(lineTotal * 100) / 100,
      vat_amount: Math.round(itemVatAmount * 100) / 100,
      total_with_vat: Math.round(totalWithVat * 100) / 100,
    };
  });
  
  return {
    items: calculatedItems,
    subtotal: Math.round(subtotal * 100) / 100,
    total_discount: Math.round(totalDiscount * 100) / 100,
    vat_amount: Math.round(vatAmount * 100) / 100,
    total_amount: Math.round((subtotal + vatAmount) * 100) / 100,
  };
}

// Log audit trail
export async function logAudit(
  companyId: string,
  userId: string,
  action: string,
  entityType: string,
  entityId: string,
  oldValues?: any,
  newValues?: any,
  req?: Request
) {
  const supabase = createSupabaseClient();

  await supabase.from('audit_logs').insert({
    company_id: companyId,
    user_id: userId,
    action,
    entity_type: entityType,
    entity_id: entityId,
    old_values: oldValues,
    new_values: newValues,
    ip_address: req?.headers.get('x-forwarded-for') || req?.headers.get('x-real-ip'),
    user_agent: req?.headers.get('user-agent'),
  });
}

// Rate limiting (simple in-memory implementation)
const rateLimitMap = new Map<string, { count: number; resetTime: number }>();

export function checkRateLimit(identifier: string, maxRequests: number, windowMs: number): boolean {
  const now = Date.now();
  const windowStart = now - windowMs;

  const current = rateLimitMap.get(identifier);

  if (!current || current.resetTime < windowStart) {
    rateLimitMap.set(identifier, { count: 1, resetTime: now + windowMs });
    return true;
  }

  if (current.count >= maxRequests) {
    return false;
  }

  current.count++;
  return true;
}

// Duplicate Detection Utility
export async function detectDuplicateExpense(
  supabase: any,
  companyId: string,
  vendorName: string,
  amount: number,
  expenseDate: string
): Promise<{ risk_level: 'none' | 'low' | 'high'; potential_duplicate_id?: string }> {
  // Normalize vendor name
  const normalizedVendor = vendorName
    .toLowerCase()
    .replace(/[^\w\s]/g, '')
    .replace(/\s+(ltd|inc|בע"מ|חברה|קבוצת).*$/i, '');

  // Query existing expenses within date range
  const dateRange = new Date(expenseDate);
  const startDate = new Date(dateRange.getTime() - 7 * 24 * 60 * 60 * 1000);
  const endDate = new Date(dateRange.getTime() + 7 * 24 * 60 * 60 * 1000);

  const { data: existingExpenses } = await supabase
    .from('expenses')
    .select('id, vendor_name, total_amount, expense_date')
    .eq('company_id', companyId)
    .neq('status', 'rejected')
    .gte('expense_date', startDate.toISOString().split('T')[0])
    .lte('expense_date', endDate.toISOString().split('T')[0]);

  if (!existingExpenses || existingExpenses.length === 0) {
    return { risk_level: 'none' };
  }

  let highestScore = 0;
  let potentialDuplicateId: string | undefined;

  for (const expense of existingExpenses) {
    // Calculate similarity scores
    const normalizedExistingVendor = expense.vendor_name
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .replace(/\s+(ltd|inc|בע"מ|חברה|קבוצת).*$/i, '');

    // Name similarity
    let nameScore = 0;
    if (normalizedVendor === normalizedExistingVendor) {
      nameScore = 100;
    } else if (normalizedVendor.includes(normalizedExistingVendor) || normalizedExistingVendor.includes(normalizedVendor)) {
      nameScore = 60;
    } else {
      // Calculate Levenshtein distance for more sophisticated matching
      const distance = levenshteinDistance(normalizedVendor, normalizedExistingVendor);
      if (distance < 3) {
        nameScore = 80;
      }
    }

    // Amount similarity
    let amountScore = 0;
    const amountDiff = Math.abs(amount - expense.total_amount) / amount;
    if (amountDiff === 0) {
      amountScore = 100;
    } else if (amountDiff <= 0.01) {
      amountScore = 90;
    } else if (amountDiff <= 0.05) {
      amountScore = 70;
    }

    // Date similarity
    let dateScore = 0;
    const daysDiff = Math.abs(new Date(expenseDate).getTime() - new Date(expense.expense_date).getTime()) / (24 * 60 * 60 * 1000);
    if (daysDiff === 0) {
      dateScore = 100;
    } else if (daysDiff <= 1) {
      dateScore = 80;
    } else if (daysDiff <= 3) {
      dateScore = 60;
    }

    // Total score
    const totalScore = (nameScore * 0.4) + (amountScore * 0.4) + (dateScore * 0.2);

    if (totalScore > highestScore) {
      highestScore = totalScore;
      potentialDuplicateId = expense.id;
    }
  }

  if (highestScore >= 95) {
    return { risk_level: 'high', potential_duplicate_id: potentialDuplicateId };
  } else if (highestScore >= 80) {
    return { risk_level: 'low', potential_duplicate_id: potentialDuplicateId };
  }

  return { risk_level: 'none' };
}

// Levenshtein distance calculation for string similarity
function levenshteinDistance(str1: string, str2: string): number {
  const matrix = [];

  for (let i = 0; i <= str2.length; i++) {
    matrix[i] = [i];
  }

  for (let j = 0; j <= str1.length; j++) {
    matrix[0][j] = j;
  }

  for (let i = 1; i <= str2.length; i++) {
    for (let j = 1; j <= str1.length; j++) {
      if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
        matrix[i][j] = matrix[i - 1][j - 1];
      } else {
        matrix[i][j] = Math.min(
          matrix[i - 1][j - 1] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j] + 1
        );
      }
    }
  }

  return matrix[str2.length][str1.length];
}

// WhatsApp Message Builder Utility
export async function buildWhatsAppMessage(
  supabase: any,
  documentId: string,
  recipientPhone: string
): Promise<{ whatsapp_url: string; message_text: string }> {
  // 1. Fetch document basic info
  const { data: document, error } = await supabase
    .from('documents')
    .select(`
      *,
      company:companies(name)
    `)
    .eq('id', documentId)
    .single();

  if (error || !document) {
    throw new Error('Document not found');
  }

  // 2. Generate public share link
  const shareUrl = await generateDocumentShareLink(supabase, documentId);
  const shortUrl = await shortenUrl(shareUrl);

  // 3. Build message text
  const documentTypeHebrew = getDocumentTypeHebrew(document.document_type);
  const formattedDate = formatDateHebrew(document.issue_date);
  const formattedAmount = formatCurrency(document.total_amount, document.currency);

  const messageText = `שלום,

מצורף ${documentTypeHebrew} מספר ${document.document_number}
מתאריך ${formattedDate}
על סך ${formattedAmount}

לצפייה והורדה:
${shortUrl}

תודה,
${document.company.name_hebrew}`;

  // 4. Encode message for URL
  const encodedMessage = encodeURIComponent(messageText);

  // 5. Build WhatsApp URLs
  const cleanPhone = recipientPhone.replace(/[^\d]/g, '');
  const whatsappUrl = `https://wa.me/${cleanPhone}?text=${encodedMessage}`;

  return {
    whatsapp_url: whatsappUrl,
    message_text: messageText
  };
}

// Helper function to generate document share link
async function generateDocumentShareLink(supabase: any, documentId: string): Promise<string> {
  // Create a signed URL valid for 30 days
  const expiresIn = 30 * 24 * 60 * 60; // 30 days in seconds

  // In a real implementation, you would create a secure share token
  const shareToken = generateSecureToken();

  // Store the share token in database
  await supabase
    .from('document_shares')
    .upsert({
      document_id: documentId,
      share_token: shareToken,
      expires_at: new Date(Date.now() + expiresIn * 1000).toISOString(),
      created_at: new Date().toISOString()
    });

  const baseUrl = Deno.env.get('SUPABASE_URL') || 'https://app.example.com';
  return `${baseUrl}/share/${shareToken}`;
}

// Helper function to shorten URL (placeholder implementation)
async function shortenUrl(url: string): Promise<string> {
  // In a real implementation, you would use bit.ly API or similar
  // For now, return the original URL
  return url;
}

// Helper function to generate secure token
function generateSecureToken(): string {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

// Helper function to get document type in Hebrew
function getDocumentTypeHebrew(documentType: string): string {
  const types: Record<string, string> = {
    'tax_invoice': 'חשבונית מס',
    'receipt': 'קבלה',
    'credit_note': 'זיכוי',
    'tax_invoice_receipt': 'חשבונית מס/קבלה'
  };
  return types[documentType] || documentType;
}

// Helper function to format date in Hebrew
function formatDateHebrew(dateString: string): string {
  const date = new Date(dateString);
  return date.toLocaleDateString('he-IL');
}

// Helper function to format currency
function formatCurrency(amount: number, currency: string): string {
  if (currency === 'ILS') {
    return `₪${amount.toFixed(2)}`;
  }
  return `${amount.toFixed(2)} ${currency}`;
}
