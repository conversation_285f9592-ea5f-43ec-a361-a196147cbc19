// Shared types for all Supabase Edge Functions

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface ValidationError {
  field: string;
  message: string;
}

export interface PaginationParams {
  page?: number;
  limit?: number;
  offset?: number;
}

// Database Types
export interface Company {
  id: string;
  business_number: string;
  name: string; // Hebrew name as primary name
  name_english?: string;
  vat_id: string;
  address_hebrew: string;
  address_english?: string;
  city_hebrew: string;
  city_english?: string;
  postal_code?: string;
  phone: string;
  email: string;
  logo_url?: string;
  subscription_tier: 'free' | 'paid';
  subscription_expires_at?: string;
  industry: string;
  annual_revenue?: string;
  interested_in_loan: boolean;
  interested_in_insurance: boolean;
  interested_in_accounting: boolean;
  created_at: string;
  updated_at: string;
}

export interface User {
  id: string;
  email: string;
  full_name: string;
  phone?: string;
  created_at: string;
  last_login_at?: string;
}

export interface CompanyUser {
  id: string;
  company_id: string;
  user_id: string;
  role: 'admin' | 'user' | 'accountant';
  created_at: string;
  created_by?: string;
}

export interface Customer {
  id: string;
  company_id: string;
  business_number: string;
  name_hebrew: string;
  name_english?: string;
  vat_id?: string;
  billing_address_hebrew: string;
  billing_address_english?: string;
  shipping_address_hebrew?: string;
  shipping_address_english?: string;
  city_hebrew: string;
  city_english?: string;
  contact_name?: string;
  contact_email?: string;
  contact_phone?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export interface Product {
  id: string;
  company_id: string;
  sku?: string;
  name_hebrew: string;
  name_english?: string;
  description_hebrew?: string;
  description_english?: string;
  unit_price: number;
  currency: string;
  vat_rate: number;
  is_service: boolean;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Document {
  id: string;
  company_id: string;
  document_type: 'tax_invoice' | 'receipt' | 'credit_note' | 'tax_invoice_receipt';
  document_number: string;
  customer_id: string;
  issue_date: string;
  due_date?: string;
  currency: string;
  subtotal: number;
  vat_amount: number;
  total_amount: number;
  status: 'draft' | 'pending_allocation' | 'approved' | 'sent' | 'paid' | 'cancelled';
  ita_allocation_number?: string;
  ita_allocation_date?: string;
  ita_submission_attempts: number;
  ita_last_error?: string;
  parent_document_id?: string;
  notes?: string;
  template_id: string;
  pdf_url?: string;
  sent_at?: string;
  sent_via?: string;
  created_by: string;
  created_at: string;
  updated_at: string;
}

export interface DocumentItem {
  id: string;
  document_id: string;
  product_id?: string;
  line_number: number;
  description_hebrew: string;
  description_english?: string;
  quantity: number;
  unit_price: number;
  currency: string;
  discount_percent: number;
  vat_rate: number;
  line_total: number;
  vat_amount: number;
  total_with_vat: number;
}

export interface Expense {
  id: string;
  company_id: string;
  expense_number: string;
  vendor_name: string;
  expense_date: string;
  amount: number;
  vat_amount: number;
  total_amount: number;
  currency: string;
  category: 'office_supplies' | 'travel' | 'utilities' | 'rent' | 'professional_services' | 'marketing' | 'equipment' | 'other';
  description?: string;
  status: 'pending' | 'approved' | 'rejected';
  duplicate_risk: 'none' | 'low' | 'high';
  duplicate_of_id?: string;
  source: 'manual' | 'email_scan' | 'upload';
  source_email_id?: string;
  original_file_url?: string;
  extracted_data?: any;
  approved_by?: string;
  approved_at?: string;
  rejection_reason?: string;
  created_at: string;
  updated_at: string;
}

// Request/Response Types
export interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
  phone: string;
  company: {
    business_number: string;
    name: string; // Hebrew name as primary name
    name_english?: string;
    vat_id: string;
    address_hebrew: string;
    city_hebrew: string;
    phone: string;
    industry: string;
    annual_revenue?: string;
    interested_in_loan: boolean;
    interested_in_insurance: boolean;
    interested_in_accounting: boolean;
  };
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface CreateDocumentRequest {
  company_id: string;
  document_type: string;
  customer_id: string;
  issue_date: string;
  due_date?: string;
  currency: string;
  items: {
    product_id?: string;
    description_hebrew: string;
    description_english?: string;
    quantity: number;
    unit_price: number;
    vat_rate: number;
    discount_percent?: number;
  }[];
  notes?: string;
  template_id?: string;
}

export interface CreateCustomerRequest {
  company_id: string;
  business_number: string;
  name_hebrew: string;
  name_english?: string;
  vat_id?: string;
  billing_address_hebrew: string;
  city_hebrew: string;
  contact_email?: string;
  contact_phone?: string;
  notes?: string;
}

export interface VATReportResponse {
  period: string;
  sales: {
    total_before_vat: number;
    vat_collected: number;
    total_with_vat: number;
    documents: any[];
  };
  purchases: {
    total_before_vat: number;
    vat_paid: number;
    total_with_vat: number;
    expenses: any[];
  };
  vat_liability: number;
  export_formats: {
    pdf_url: string;
    excel_url: string;
    pcn874_url: string;
  };
}
